#!/usr/bin/env python3
# SPDX-License-Identifier: Apache-2.0
"""
使用vLLM Python API批量测试不同prefix cache命中率的性能脚本

优势：
1. 不需要启动独立的vLLM服务
2. 不需要设置VLLM_SERVER_DEV_MODE=1
3. 直接调用LLM.reset_prefix_cache()方法
4. 更高的性能和更低的延迟

使用方法:
python batch_test_hit_rates_python_api.py --model your_model --hit-rates 0.0 0.3 0.5 0.7 0.9
"""

import argparse
import json
import os
import sys
import time
from datetime import datetime
from typing import Any, Dict, List

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from benchmark_prefix_caching import generate_prefix_cache_dataset
from backend_request_func import get_tokenizer

# vLLM imports
from vllm import LLM, SamplingParams
from vllm.engine.arg_utils import EngineArgs


def test_single_hit_rate_python_api(
    hit_rate: float,
    llm: LLM,
    args: argparse.Namespace,
    tokenizer,
) -> Dict[str, Any]:
    """使用Python API测试单个命中率的性能"""
    
    print(f"\n{'='*80}")
    print(f"测试命中率: {hit_rate:.1%}")
    print(f"{'='*80}")
    
    # 重置prefix cache
    print("重置Prefix Cache...")
    reset_success = llm.reset_prefix_cache()
    if reset_success:
        print("✓ Prefix cache重置成功")
    else:
        print("✗ Prefix cache重置失败，可能影响测试结果")
    
    # 生成warmup requests
    num_warmup_requests = args.num_prompts
    warmup_requests = generate_prefix_cache_dataset(
        tokenizer=tokenizer,
        num_requests=num_warmup_requests,
        target_hit_rate=0.0,  # Warmup阶段不需要cache hit
        input_len=args.random_input_len,
        output_len=1,  # Warmup阶段只输出1个token，最小化开销
        seed=args.seed,
        mode="warmup"
    )
    
    # 生成test requests（基于warmup requests）
    num_test_requests = args.num_prompts
    test_requests = generate_prefix_cache_dataset(
        tokenizer=tokenizer,
        num_requests=num_test_requests,
        target_hit_rate=hit_rate,
        input_len=args.random_input_len,
        output_len=args.random_output_len,
        seed=args.seed,
        warmup_requests=warmup_requests,
        mode="test"
    )
    
    # 设置采样参数
    warmup_sampling_params = SamplingParams(
        temperature=0.0,
        max_tokens=1,  # Warmup阶段只生成1个token
    )
    
    test_sampling_params = SamplingParams(
        temperature=args.temperature if args.temperature is not None else 0.0,
        max_tokens=args.random_output_len,
        top_p=args.top_p,
        top_k=args.top_k,
        min_p=args.min_p,
    )
    
    print(f"\n阶段1: Warmup - 填充prefix cache")
    print(f"处理 {len(warmup_requests)} 个warmup请求...")
    
    # Warmup阶段
    warmup_start_time = time.time()
    warmup_prompts = [req.prompt for req in warmup_requests]
    warmup_outputs = llm.generate(warmup_prompts, warmup_sampling_params)
    warmup_end_time = time.time()
    warmup_duration = warmup_end_time - warmup_start_time
    
    print(f"Warmup完成，耗时: {warmup_duration:.2f}s")
    print(f"等待系统稳定...")
    time.sleep(1)  # 等待1秒让系统稳定
    
    print(f"\n阶段2: Test - 测量prefix cache性能")
    print(f"处理 {len(test_requests)} 个test请求...")
    
    # Test阶段
    test_start_time = time.time()
    test_prompts = [req.prompt for req in test_requests]
    test_outputs = llm.generate(test_prompts, test_sampling_params)
    test_end_time = time.time()
    test_duration = test_end_time - test_start_time
    
    # 计算性能指标
    total_input_tokens = sum(len(tokenizer.encode(prompt)) for prompt in test_prompts)
    total_output_tokens = sum(len(output.outputs[0].token_ids) for output in test_outputs)
    
    request_throughput = len(test_outputs) / test_duration
    output_throughput = total_output_tokens / test_duration
    total_token_throughput = (total_input_tokens + total_output_tokens) / test_duration
    
    # 计算延迟指标
    ttfts = []  # Time to first token
    tpots = []  # Time per output token
    
    for output in test_outputs:
        if hasattr(output, 'metrics') and output.metrics:
            if hasattr(output.metrics, 'first_token_time'):
                ttfts.append(output.metrics.first_token_time * 1000)  # 转换为ms
            if hasattr(output.metrics, 'time_per_output_token'):
                tpots.append(output.metrics.time_per_output_token * 1000)  # 转换为ms
    
    result = {
        "target_hit_rate": hit_rate,
        "completed": len(test_outputs),
        "total_input_tokens": total_input_tokens,
        "total_output_tokens": total_output_tokens,
        "request_throughput": request_throughput,
        "output_throughput": output_throughput,
        "total_token_throughput": total_token_throughput,
        "test_duration": test_duration,
        "warmup_duration": warmup_duration,
        "total_test_time": warmup_duration + test_duration,
        "mean_ttft_ms": sum(ttfts) / len(ttfts) if ttfts else None,
        "mean_tpot_ms": sum(tpots) / len(tpots) if tpots else None,
    }
    
    print(f"\n命中率 {hit_rate:.1%} 测试完成:")
    print(f"  - 请求吞吐量: {request_throughput:.2f} req/s")
    print(f"  - 输出token吞吐量: {output_throughput:.2f} tok/s")
    print(f"  - 总token吞吐量: {total_token_throughput:.2f} tok/s")
    print(f"  - 测试时间: {test_duration:.2f}s (warmup: {warmup_duration:.2f}s)")
    
    return result


def main():
    parser = argparse.ArgumentParser(description="使用Python API批量测试不同prefix cache命中率的性能")
    
    # 基本参数
    parser.add_argument("--model", type=str, required=True, help="模型名称或路径")
    parser.add_argument("--tokenizer", type=str, help="分词器路径")
    
    # 测试参数
    parser.add_argument("--hit-rates", type=float, nargs="+", 
                       default=[0.0, 0.3, 0.5, 0.7, 0.9],
                       help="要测试的命中率列表")
    parser.add_argument("--num-prompts", type=int, default=100, help="每个测试的请求数量")
    parser.add_argument("--random-input-len", type=int, default=1024, help="输入长度")
    parser.add_argument("--random-output-len", type=int, default=128, help="输出长度")
    parser.add_argument("--seed", type=int, default=0, help="随机种子")
    
    # vLLM引擎参数
    parser.add_argument("--tensor-parallel-size", type=int, default=1, help="张量并行大小")
    parser.add_argument("--dtype", type=str, default="auto", help="数据类型")
    parser.add_argument("--gpu-memory-utilization", type=float, default=0.9, help="GPU内存利用率")
    parser.add_argument("--max-model-len", type=int, help="最大模型长度")
    parser.add_argument("--block-size", type=int, default=16, help="KV cache块大小")
    parser.add_argument("--enforce-eager", action="store_true", help="强制使用eager模式")
    
    # 采样参数
    parser.add_argument("--temperature", type=float, help="采样温度")
    parser.add_argument("--top-p", type=float, help="Top-p采样")
    parser.add_argument("--top-k", type=int, help="Top-k采样")
    parser.add_argument("--min-p", type=float, help="Min-p采样")
    
    # 其他参数
    parser.add_argument("--trust-remote-code", action="store_true", help="信任远程代码")
    parser.add_argument("--output-file", type=str, help="结果输出文件")
    
    args = parser.parse_args()
    
    # 获取分词器
    tokenizer_id = args.tokenizer if args.tokenizer else args.model
    tokenizer = get_tokenizer(tokenizer_id, trust_remote_code=args.trust_remote_code)
    
    print(f"初始化vLLM引擎...")
    print(f"模型: {args.model}")
    print(f"启用prefix caching: True")
    print(f"张量并行大小: {args.tensor_parallel_size}")
    print(f"数据类型: {args.dtype}")
    
    # 创建LLM实例
    llm = LLM(
        model=args.model,
        tokenizer=tokenizer_id,
        tensor_parallel_size=args.tensor_parallel_size,
        dtype=args.dtype,
        gpu_memory_utilization=args.gpu_memory_utilization,
        max_model_len=args.max_model_len,
        block_size=args.block_size,
        enable_prefix_caching=True,  # 启用prefix caching
        enforce_eager=args.enforce_eager,
        trust_remote_code=args.trust_remote_code,
    )
    
    print(f"\n开始批量测试 {len(args.hit_rates)} 个命中率: {args.hit_rates}")
    print(f"每个测试请求数: {args.num_prompts}")
    print(f"输入长度: {args.random_input_len}, 输出长度: {args.random_output_len}")
    
    # 执行所有测试
    all_results = []
    for hit_rate in args.hit_rates:
        try:
            result = test_single_hit_rate_python_api(
                hit_rate=hit_rate,
                llm=llm,
                args=args,
                tokenizer=tokenizer,
            )
            all_results.append(result)
        except Exception as e:
            print(f"测试命中率 {hit_rate:.1%} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    # 保存结果
    if args.output_file:
        output_file = args.output_file
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"batch_hit_rate_results_python_api_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            "test_config": vars(args),
            "results": all_results,
            "summary": {
                "total_tests": len(all_results),
                "hit_rates_tested": [r["target_hit_rate"] for r in all_results],
                "test_timestamp": datetime.now().isoformat(),
                "api_type": "python_api",
            }
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n{'='*80}")
    print("批量测试完成!")
    print(f"结果已保存到: {output_file}")
    print(f"总共测试了 {len(all_results)} 个命中率")
    
    # 打印简要总结
    print(f"\n{'命中率':<10} {'吞吐量(req/s)':<15} {'输出吞吐量(tok/s)':<20} {'测试时间(s)':<12}")
    print("-" * 60)
    for result in all_results:
        hit_rate = result["target_hit_rate"]
        req_throughput = result["request_throughput"]
        output_throughput = result["output_throughput"]
        test_time = result["total_test_time"]
        print(f"{hit_rate:<10.1%} {req_throughput:<15.2f} {output_throughput:<20.2f} {test_time:<12.2f}")


if __name__ == "__main__":
    main()
